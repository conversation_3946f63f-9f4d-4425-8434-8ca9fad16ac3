import { Link } from '@tanstack/react-router';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { adminRegistrationSchema } from '../../schema/adminRegistrationSchema';
import { useRegisterAdmin } from '../../hooks/useRegisterAdmin';
import { useNavigate } from '@tanstack/react-router';
import { z } from 'zod';
import { toast } from 'react-toastify';

const Signup = () => {
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<z.infer<typeof adminRegistrationSchema>>({
    resolver: zodResolver(adminRegistrationSchema),
  });

  const { mutate: registerAdmin, isPending } = useRegisterAdmin();

  const onSubmit = (data: z.infer<typeof adminRegistrationSchema>) => {
    registerAdmin(data, {
      onSuccess: () => {
        toast.success('Registration successful!');
        navigate({ to: '/auth/login' });
      },
      onError: (error: any) => {
        const errorMessage = error?.response?.data?.detail || 'Registration failed. Please try again.';
        toast.error(errorMessage);
      },
    });
  };

  return (
    <section className="grid grid-cols-2 gap-x-20 pt-32 relative">
      <section className="h-64 flex items-center justify-center">
        <div className="max-w-xl rounded text-white">
          <div className="flex items-center mb-4">
            <div className="mr-3 w-20 h-20">
              <img
                src="/expenso-logo.png"
                alt="Expenso Logo"
                className="w-full h-full object-contain"
              />
            </div>

            <div className="w-70 h-10">
              <img src="/expenso-text.svg" alt="EXPENSO" className="w-full h-full object-contain" />
            </div>
          </div>

          <p className="text-xs font-medium uppercase tracking-[1.4em] mb-6">Admin Dashboard</p>

          <h1 className="font-semibold mb-2 text-4xl">Sign Up to</h1>

          <p className="text-lg font-light my-4">
            Streamline your company’s expense management <br />
            in one secure platform.
          </p>

          <p className="text-sm font-light">
            Sign up to get started and take control of your <br />
            expenses today.
          </p>
        </div>
      </section>

      <section className="z-20 mx-auto bg-white rounded-2xl p-12 shadow-lg absolute right-65 top-4/10">
        <div className="flex justify-between mb-2">
          <h3 className="text-xl font-normal">
            Welcome to <span className="text-[#0ee6c9] font-semibold">EXPENSO</span>
          </h3>
          <p className="text-sm text-gray-500">
            Have an Account? <br />
            <Link to="/auth/login" className="text-[#0ee6c9] font-medium hover:underline">
              Sign in
            </Link>
          </p>
        </div>

        <h1 className="text-6xl font-semibold mb-12">Sign up</h1>

        <form className="grid gap-8 w-110" onSubmit={handleSubmit(onSubmit)}>
          <div className="grid gap-2">
            <label htmlFor="email" className="text-sm font-normal text-gray-800">
              Enter your email address
            </label>
            <input
              {...register('email')}
              id="email"
              type="email"
              placeholder="email address"
              className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
            />
            {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <label htmlFor="username" className="text-sm font-normal text-gray-800">
                User name
              </label>
              <input
                {...register('username')}
                id="username"
                type="text"
                placeholder="User name"
                className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
              />
              {errors.username && (
                <p className="text-red-500 text-xs mt-1">{errors.username.message}</p>
              )}
            </div>
            <div className="grid gap-2">
              <label htmlFor="first_name" className="text-sm font-normal text-gray-700">
                First Name
              </label>
              <input
                {...register('first_name')}
                id="first_name"
                type="text"
                placeholder="Enter your first name"
                className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
              />
              {errors.first_name && (
                <p className="text-red-500 text-xs mt-1">{errors.first_name.message}</p>
              )}
            </div>

            <div className="grid gap-2">
              <label htmlFor="last_name" className="text-sm font-normal text-gray-700">
                Last Name
              </label>
              <input
                {...register('last_name')}
                id="last_name"
                type="text"
                placeholder="Enter your last name"
                className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="contact" className="text-sm font-normal text-gray-800">
                Phone Number
              </label>
              <input
                {...register('phone')}
                id="phone"
                type="number"
                placeholder="Phone Number"
                className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
              />
              {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone.message}</p>}
            </div>
          </div>

          <div className="grid gap-2">
            <label htmlFor="password" className="text-sm font-normal text-gray-700">
              Enter your Password
            </label>
            <input
              {...register('password')}
              id="password"
              type="password"
              placeholder="Password"
              className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
            />
            {errors.password && (
              <p className="text-red-500 text-xs mt-1">{errors.password.message}</p>
            )}
          </div>
          <div className="grid gap-2">
            <label htmlFor="confirm_password" className="text-sm font-normal text-gray-700">
              Confirm your Password
            </label>
            <input
              {...register('confirm_password')}
              id="confirm_password"
              type="password"
              placeholder="Confirm Password"
              className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
            />
            {errors.confirm_password && (
              <p className="text-red-500 text-xs mt-1">{errors.confirm_password.message}</p>
            )}
          </div>

          <button
            type="submit"
            disabled={isPending}
            className="brand-gradient mt-10 rounded-lg text-white py-3 font-semibold hover:opacity-90 transition flex items-center justify-center"
          >
            {isPending ? (
              <svg className="animate-spin h-5 w-5 mr-2 text-white" viewBox="0 0 24 24">
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z"></path>
              </svg>
            ) : null}
            Sign up
          </button>
        </form>
      </section>
    </section>
  );
};

export default Signup;
