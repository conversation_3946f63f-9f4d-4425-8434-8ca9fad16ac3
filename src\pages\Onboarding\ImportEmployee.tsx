import React, { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { Download, Upload, X } from 'lucide-react';
import { toast } from 'react-toastify';
import * as <PERSON> from 'papaparse';
import { useNavigate } from '@tanstack/react-router';

type EmployeeData = {
  'Employee Name': string;
  Email: string;
  Department: string;
  Role: string;
  Manager?: string;
  'Employee ID'?: string;
};

type FormData = {
  csvFile: FileList;
};

const ImportEmployees = () => {
  const navigate = useNavigate();
  const [csvData, setCsvData] = useState<EmployeeData[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [fileName, setFileName] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<FormData>();

  const downloadSampleCSV = () => {
    const sampleData = [
      {
        'Employee Name': '<PERSON>',
        Email: '<EMAIL>',
        Department: 'Engineering',
        Role: 'Software Engineer',
        Manager: '<PERSON>',
        'Employee ID': 'EMP001',
      },
      {
        'Employee Name': '<PERSON> Bennett',
        Email: '<EMAIL>',
        Department: 'Marketing',
        Role: 'Marketing Manager',
        Manager: 'John Doe',
        'Employee ID': 'EMP002',
      },
      {
        'Employee Name': 'Ethan Harper',
        Email: '<EMAIL>',
        Department: 'Sales',
        Role: 'Sales Representative',
        Manager: 'Jane Smith',
        'Employee ID': 'EMP003',
      },
    ];

    const csv = Papa.unparse(sampleData);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'sample_employees.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const parseCSV = useCallback((file: File) => {
    setIsLoading(true);
    Papa.parse<EmployeeData>(file, {
      complete: (results) => {
        const data = results.data;

        const filteredData = data.filter(
          (row: EmployeeData) =>
            row['Employee Name'] && row['Email'] && row['Department'] && row['Role']
        );
        setCsvData(filteredData);
        setFileName(file.name);
        setIsLoading(false);
      },
      header: true,
      skipEmptyLines: true,
      error: (error) => {
        console.error('Error parsing CSV:', error);
        setIsLoading(false);
      },
    });
  }, []);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (e.dataTransfer.files && e.dataTransfer.files[0]) {
        const file = e.dataTransfer.files[0];
        if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
          setValue('csvFile', e.dataTransfer.files);
          parseCSV(file);
        } else {
          toast.warning('Please select a CSV file');
        }
      }
    },
    [setValue, parseCSV]
  );

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
        parseCSV(file);
      } else {
        toast.warning('Please select a CSV file');
      }
    }
  };

  const removeFile = () => {
    setValue('csvFile', {} as FileList);
    setCsvData([]);
    setFileName('');
  };

  const onSubmit = async () => {
    if (csvData.length === 0) {
      toast.error('Please upload a CSV file first');
      return;
    }

    try {
      console.log('CSV data to be sent to API:', csvData);
      toast.success(`Successfully imported ${csvData.length} employees!`);
      navigate({ to: '/onboarding/logo-upload' });
    } catch (error) {
      console.error('Error importing employees:', error);
      toast.error('Error importing employees. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-10 pb-6">
          <div className="flex justify-between items-center mt-8">
            <div className="flex flex-col">
              <div className="flex items-center mb-2">
                <div className="w-14 h-14 mr-3">
                  <img src="/expenso-logo.png" alt="Expenso Logo" className="w-full h-full" />
                </div>

                <div className="w-50 h-10">
                  <img src="/expenso-text.svg" alt="EXPENSO" className="w-full h-full" />
                </div>
              </div>

              <p className="text-xs font-medium text-gray-500 uppercase tracking-[0.9em] ">
                Admin Dashboard
              </p>
            </div>

            <div className="flex flex-col items-start space-y-1">
              <p className="text-sm font-semibold text-gray-900">Setup Progress</p>
              <p className="text-xs text-gray-500">2 of 4 steps completed</p>
              <div className="flex space-x-2">
                {[1, 2, 3, 4].map((step) => (
                  <div
                    key={step}
                    className={`h-2 w-12 rounded-full ${
                      step <= 2 ? 'bg-[#06B217]' : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="flex items-center mt-10">
            <div className="w-5 h-5 bg-white rounded-full" />
          </div>
          <h1 className="text-3xl font-semibold text-gray-600">Import Employee</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div className="bg-white rounded-lg p-10">
          <div className="mb-8">
            <p className="text-gray-600 text-base leading-relaxed">
              Import employee data from a CSV file. Ensure your file includes columns for employee
              name, email, department, and role.{' '}
              <button
                onClick={downloadSampleCSV}
                className="text-teal-500 hover:text-teal-600 font-medium underline"
              >
                Download a sample CSV
              </button>{' '}
              to see the required format.
            </p>
          </div>

          <div>
            <div className="mb-8 sm:mb-12">
              <div className="flex justify-end mb-4">
                <button
                  type="button"
                  onClick={downloadSampleCSV}
                  className="flex items-center px-4 py-2 brand-gradient text-white rounded-3xl hover:bg-gray-900 transition-colors"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download Sample CSV
                </button>
              </div>

              <div
                className={`relative border-2 border-dashed rounded-lg p-12 text-center transition-colors ${
                  dragActive
                    ? 'border-teal-500 bg-teal-50'
                    : fileName
                      ? 'border-green-300 bg-green-50'
                      : 'border-gray-300 bg-gray-50'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <input
                  {...register('csvFile', { required: 'Please select a CSV file' })}
                  type="file"
                  accept=".csv"
                  onChange={handleFileSelect}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />

                {fileName ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-center space-x-2">
                      <Upload className="w-8 h-8 text-green-500" />
                      <div>
                        <p className="text-lg font-medium text-gray-900">{fileName}</p>
                        <p className="text-sm text-gray-500">{csvData.length} employees found</p>
                      </div>
                      <button
                        type="button"
                        onClick={removeFile}
                        className="ml-4 p-1 text-gray-400 hover:text-red-500 transition-colors"
                      >
                        <X className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4 sm:space-y-3">
                    <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-lg font-medium text-gray-900">
                        Drag and drop a CSV file here
                      </p>
                      <p className="text-sm text-gray-500 mt-1">Or click to browse</p>
                    </div>
                    <button
                      type="button"
                      className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      Select CSV File
                    </button>
                  </div>
                )}

                {isLoading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-500"></div>
                  </div>
                )}
              </div>

              {errors.csvFile && (
                <p className="mt-2 text-sm text-red-600">{errors.csvFile.message}</p>
              )}
            </div>

            {csvData.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Data Preview</h3>
                <div className="overflow-x-auto border border-gray-200 rounded-lg">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Employee Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Email
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Department
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Role
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {csvData.slice(0, 10).map((employee, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {employee['Employee Name']}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                            {employee['Email']}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {employee['Department']}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {employee['Role']}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  {csvData.length > 10 && (
                    <div className="px-6 py-3 bg-gray-50 text-sm text-gray-500 text-center">
                      Showing first 10 of {csvData.length} employees
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="flex justify-end">
              <button
                type="button"
                onClick={handleSubmit(onSubmit)}
                disabled={csvData.length === 0}
                className="px-8 py-3 bg-teal-500 text-white font-medium rounded-lg hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Import Employees
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ImportEmployees;
