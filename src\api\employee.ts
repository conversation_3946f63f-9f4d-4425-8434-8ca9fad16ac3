
import api from "../lib/axiosInstance";
import { z } from "zod";
import { employeeRegistrationSchema } from "../schema/employeeRegistrationSchema";


export type EmployeeRegistrationInput  = z.infer<typeof employeeRegistrationSchema >;


export const registerEmployee = async (data: EmployeeRegistrationInput) => {
  const response = await api.post("/api/core/users/", data);
  return response.data;
};
