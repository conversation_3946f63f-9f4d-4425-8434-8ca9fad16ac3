import { useState } from 'react';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { expenseData, statsData, approvalData, disputeData } from '../../mockData/mockData';
import StatsCard from './components/StatsCard';
import Tabs from './components/Tabs';
import TabContent from './components/TabContent';
import { Button } from '../../components/Button';
import { useNavigate } from '@tanstack/react-router';

const ExpenseReports = () => {
  const [activeTab, setActiveTab] = useState<'all' | 'approvals' | 'disputes'>('all');
  const navigate = useNavigate();

  return (
    <div className="p-4 sm:p-6 bg-gray-50">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Expense Reports</h1>
          <Breadcrumb pageName="Expense Reports" />
        </div>
        <Button
          className="brand-gradient px-8 py-3 rounded-sm text-sm text-white"
          onClick={() => navigate({ to: '/expense-reports/batch-export' })}
        >
          Batch Export
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {statsData.map((stat, index) => (
          <StatsCard key={index} {...stat} />
        ))}
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <Tabs activeTab={activeTab} onChange={setActiveTab} />
        <TabContent tabCategory="all" activeTab={activeTab} expenseData={expenseData} />
        <TabContent tabCategory="approvals" activeTab={activeTab} approvalData={approvalData} />
        <TabContent tabCategory="disputes" activeTab={activeTab} disputeData={disputeData} />
      </div>
    </div>
  );
};

export default ExpenseReports;
