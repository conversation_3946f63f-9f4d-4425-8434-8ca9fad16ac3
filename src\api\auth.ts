import api from '../lib/axiosInstance';
import { z } from 'zod';
import { adminRegistrationSchema } from '../schema/adminRegistrationSchema';
import { adminLoginSchema } from '../schema/adminLoginSchema';

export type AdminRegisterInput = z.infer<typeof adminRegistrationSchema>;
export type AdminLoginInput = z.infer<typeof adminLoginSchema>;

export const registerAdmin = async (data: AdminRegisterInput) => {
  const response = await api.post('/api/auth/register/admin/', data);
  return response.data;
};

export const loginAdmin = async (data: AdminLoginInput) => {
  const response = await api.post('/api/auth/login/', data);
  return response.data;
};
