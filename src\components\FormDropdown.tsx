import { useState } from 'react';
import { ChevronDown } from 'lucide-react';

interface DropdownOption {
  label: string;
  value: string | number;
}

interface DropdownProps {
  value: string | number;
  onChange: (value: string | number) => void;
  options: DropdownOption[];
  label: string;
  className?: string;
  placeholder?: string;
  required?: boolean;
  dropdownSize?: 'sm' | 'md' | 'lg';
  error?: string;
}

export default function FormDropdown({
  value,
  onChange,
  options,
  label,
  className = '',
  placeholder = `Select ${label.toLowerCase()}`,
  required = false,
  dropdownSize = 'md',
  error,
}: DropdownProps) {
  const [open, setOpen] = useState(false);
  const displayLabel = options.find((opt) => opt.value === value)?.label || placeholder;

  const handleSelect = (val: string | number) => {
    onChange(val);
    setOpen(false);
  };

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-5 py-4 text-lg',
  };

  return (
    <div className={`w-full ${className}`}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      <div
        tabIndex={0}
        onBlur={(e) => {
          if (!e.currentTarget.contains(e.relatedTarget)) {
            setOpen(false);
          }
        }}
        className="relative"
      >
        <button
          type="button"
          onClick={() => setOpen((prev) => !prev)}
          className={`w-full text-left border ${
            error ? 'border-red-500' : 'border-gray-300'
          } rounded-md text-sm font-semibold bg-gray-50 focus:outline-none focus:ring-2 ${
            error ? 'focus:ring-red-500' : 'focus:ring-blue-500'
          } focus:border-transparent transition flex items-center justify-between ${
            sizeClasses[dropdownSize]
          } ${value ? 'text-gray-800' : 'text-gray-500'}`}
        >
          <span className="text-gray-500">{displayLabel}</span>
          <ChevronDown
            className={`w-4 h-4 ml-2 transition-transform duration-200 ${open ? 'rotate-180' : ''}`}
          />
        </button>

        {open && (
          <div className="absolute z-20 mt-2 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto w-full overscroll-contain">
            {options.map((opt) => (
              <div
                key={opt.value}
                onClick={() => handleSelect(opt.value)}
                className={`px-4 py-2 cursor-pointer text-sm hover:bg-blue-50 ${
                  value === opt.value ? 'bg-blue-100 font-medium' : ''
                }`}
              >
                {opt.label}
              </div>
            ))}
          </div>
        )}
      </div>

      {error && <p className="text-red-500 text-sm mt-1 font-medium">{error}</p>}
    </div>
  );
}
