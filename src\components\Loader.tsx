import React from 'react';
import { motion } from 'framer-motion';

interface LoaderProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  text?: string;
  className?: string;
  variant?: 'orbital' | 'grid' | 'pulse' | 'wave';
  color?: 'teal' | 'blue' | 'gray' | 'green';
}

const Loader: React.FC<LoaderProps> = ({
  size = 'md',
  text = 'Please wait...',
  className = '',
  variant = 'orbital',
  color = 'teal',
}) => {
  const sizeStyles = {
    sm: { spinner: 'w-6 h-6', dots: 'w-2 h-2', text: 'text-xs' },
    md: { spinner: 'w-10 h-10', dots: 'w-3 h-3', text: 'text-sm' },
    lg: { spinner: 'w-14 h-14', dots: 'w-4 h-4', text: 'text-base' },
    xl: { spinner: 'w-18 h-18', dots: 'w-5 h-5', text: 'text-lg' },
  };

  const colorStyles = {
    teal: {
      primary: 'text-teal-500',
      secondary: 'text-teal-200',
      accent: 'brand-gra',
      gradient: 'from-teal-400 to-teal-600',
      text: 'text-teal-600',
    },
    blue: {
      primary: 'text-blue-500',
      secondary: 'text-blue-200',
      accent: 'bg-blue-500',
      gradient: 'from-blue-400 to-blue-600',
      text: 'text-blue-600',
    },
    gray: {
      primary: 'text-gray-500',
      secondary: 'text-gray-200',
      accent: 'bg-gray-500',
      gradient: 'from-gray-400 to-gray-600',
      text: 'text-gray-600',
    },
    green: {
      primary: 'text-green-500',
      secondary: 'text-green-200',
      accent: 'bg-green-500',
      gradient: 'from-green-400 to-green-600',
      text: 'text-green-600',
    },
  };

  const OrbitalLoader = () => (
    <div className={`relative ${sizeStyles[size].spinner}`}>
      <motion.div
        className={`absolute inset-0 rounded-full border-2 border-transparent border-t-current ${colorStyles[color].primary}`}
        animate={{ rotate: 360 }}
        transition={{
          repeat: Infinity,
          ease: 'linear',
          duration: 1.5,
        }}
      />

      <motion.div
        className={`absolute inset-2 rounded-full border-2 border-transparent border-b-current ${colorStyles[color].primary} opacity-60`}
        animate={{ rotate: -360 }}
        transition={{
          repeat: Infinity,
          ease: 'linear',
          duration: 2,
        }}
      />

      <motion.div
        className={`absolute top-1/2 left-1/2 w-2 h-2 -mt-1 -ml-1 rounded-full ${colorStyles[color].accent}`}
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.6, 1, 0.6],
        }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />
    </div>
  );
  
  const GridLoader = () => (
    <div className="grid grid-cols-3 gap-1">
      {Array.from({ length: 9 }).map((_, index) => (
        <motion.div
          key={index}
          className={`rounded-sm ${colorStyles[color].accent} ${sizeStyles[size].dots}`}
          animate={{
            scale: [0.8, 1.2, 0.8],
            opacity: [0.3, 1, 0.3],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: (index % 3) * 0.1 + Math.floor(index / 3) * 0.2,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );

  const PulseLoader = () => (
    <div className={`relative ${sizeStyles[size].spinner}`}>
      <motion.div
        className={`absolute inset-0 rounded-full bg-gradient-to-r ${colorStyles[color].gradient} opacity-20`}
        animate={{
          scale: [1, 1.8, 1],
          opacity: [0.5, 0.1, 0.5],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />

      <motion.div
        className={`absolute inset-2 rounded-full bg-gradient-to-r ${colorStyles[color].gradient} opacity-40`}
        animate={{
          scale: [1, 1.4, 1],
          opacity: [0.7, 0.2, 0.7],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 0.2,
        }}
      />

      <motion.div
        className={`absolute inset-4 rounded-full bg-gradient-to-r ${colorStyles[color].gradient}`}
        animate={{
          scale: [1, 1.1, 1],
          opacity: [0.8, 1, 0.8],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 0.4,
        }}
      />
    </div>
  );
  const WaveLoader = () => (
    <div className="flex items-center space-x-1">
      {[0, 1, 2, 3, 4].map((index) => (
        <motion.div
          key={index}
          className={`rounded-full ${colorStyles[color].accent} ${sizeStyles[size].dots}`}
          animate={{
            y: [-8, 0, -8],
            opacity: [0.4, 1, 0.4],
            scale: [0.8, 1.2, 0.8],
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            delay: index * 0.1,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );

  const renderLoader = () => {
    switch (variant) {
      case 'grid':
        return <GridLoader />;
      case 'pulse':
        return <PulseLoader />;
      case 'wave':
        return <WaveLoader />;
      default:
        return <OrbitalLoader />;
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center gap-6 py-8 ${className}`}>
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, ease: 'easeOut' }}
        className="relative"
      >
        {renderLoader()}
      </motion.div>

      {text && (
        <motion.p
          className={`font-semibold tracking-wide ${colorStyles[color].text} ${sizeStyles[size].text}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2, ease: 'easeOut' }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );
};

export default Loader;
