import { ChevronDown } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from '@tanstack/react-router';
import { employeeRegistrationSchema } from '../../schema/employeeRegistrationSchema';
import { useRegisterEmployee } from '../../hooks/useRegisterEmployee';
import { AxiosError } from 'axios';
import { toast } from 'react-toastify';
import { useDepartments } from '../../hooks/useDepartments';
import { z } from 'zod';

const AddEmployee = () => {
  const navigate = useNavigate();
  const { data: departments } = useDepartments();

  const { mutate, isPending } = useRegisterEmployee();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<z.infer<typeof employeeRegistrationSchema>>({
    resolver: zodResolver(employeeRegistrationSchema),
  });

  const onSubmit = (data: z.infer<typeof employeeRegistrationSchema>) => {
    const payload = {
      ...data,
    };

    mutate(payload, {
      onSuccess: () => {
        toast.success('Employee added successfully!');
        navigate({ to: '/onboarding/logo-upload' });
      },
      onError: (error: unknown) => {
        const axiosError = error as AxiosError<{ message?: string }>;
        const message = axiosError.response?.data?.message || 'Unknown error occurred';
        toast.error('Something went wrong: ' + message);
      },
    });
  };

  return (
    <div className="min-h-screen">
      <header className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-10 pb-6">
          <div className="flex justify-between items-center mt-8">
            <div className="flex flex-col">
              <div className="flex items-center mb-2">
                <div className="w-14 h-14 mr-3">
                  <img src="/expenso-logo.png" alt="Expenso Logo" className="w-full h-full" />
                </div>

                <div className="w-50 h-10">
                  <img src="/expenso-text.svg" alt="EXPENSO" className="w-full h-full" />
                </div>
              </div>

              <p className="text-xs font-medium text-gray-500 uppercase tracking-[0.9em] ">
                Admin Dashboard
              </p>
            </div>

            <div className="flex flex-col items-start space-y-1">
              <p className="text-sm font-semibold text-gray-900">Setup Progress</p>
              <p className="text-xs text-gray-500">2 of 4 steps completed</p>
              <div className="flex space-x-2">
                {[1, 2, 3, 4].map((step) => (
                  <div
                    key={step}
                    className={`h-2 w-12 rounded-full ${
                      step <= 2 ? 'bg-[#06B217]' : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="flex items-center mt-10">
            <div className="w-5 h-5 bg-white rounded-full" />
          </div>
          <h1 className="text-3xl font-semibold text-gray-600">Add Employee</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div className="bg-white rounded-lg p-10">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div className="space-y-8">
                  <div>
                    <label className="block text-base font-medium text-gray-800 mb-3">
                      First Name
                    </label>
                    <input
                      {...register('first_name')}
                      type="text"
                      placeholder="Enter first name"
                      className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors placeholder-gray-500 text-base"
                    />
                    {errors.first_name && (
                      <p className="text-red-500 text-sm">{errors.first_name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-base font-medium text-gray-800 mb-3">
                      Username
                    </label>
                    <input
                      type="text"
                      placeholder="Enter username"
                      {...register('username')}
                      className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors placeholder-gray-500 text-base"
                    />
                    {errors.username && (
                      <p className="text-red-500 text-sm">{errors.username.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-base font-medium text-gray-800 mb-3">
                      Last Name
                    </label>
                    <input
                      type="text"
                      placeholder="Enter last name"
                      {...register('last_name')}
                      className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors placeholder-gray-500 text-base"
                    />
                    {errors.last_name && (
                      <p className="text-red-500 text-sm">{errors.last_name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-base font-medium text-gray-800 mb-3">
                      Email Address
                    </label>
                    <input
                      type="email"
                      placeholder="Enter email address"
                      {...register('email')}
                      className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors placeholder-gray-500 text-base"
                    />
                    {errors.email && <p className="text-red-500 text-sm">{errors.email.message}</p>}
                  </div>

                  <div>
                    <label className="block text-base font-medium text-gray-800 mb-3">
                      Department
                    </label>
                    <div className="relative">
                      <select
                        {...register('department')}
                        className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors appearance-none bg-white text-gray-500 text-base"
                      >
                        <option value="">Select Department</option>
                        {departments?.map((dept) => (
                          <option key={dept.id} value={dept.id}>
                            {dept.name}
                          </option>
                        ))}
                      </select>
                      <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
                    </div>
                  </div>
                </div>

                <div className="space-y-8">
                  <div>
                    <label className="block text-base font-medium text-gray-800 mb-3">
                      Manager
                    </label>
                    <div className="relative">
                      <select
                        name="manager"
                        className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors appearance-none bg-white text-gray-500 text-base"
                      >
                        <option value="">Select manager</option>
                        <option value="john-doe">John Doe</option>
                        <option value="jane-smith">Jane Smith</option>
                        <option value="mike-johnson">Mike Johnson</option>
                        <option value="sarah-wilson">Sarah Wilson</option>
                      </select>
                      <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
                    </div>
                  </div>

                  <div>
                    <label className="block text-base font-medium text-gray-800 mb-3">
                      Employee ID
                    </label>
                    <input
                      name="employeeId"
                      type="text"
                      placeholder="Enter employee ID"
                      className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors placeholder-gray-500 text-base"
                    />
                  </div>

                  {/* <div>
                    <label className="block text-base font-medium text-gray-800 mb-3">Role</label>
                    <div className="relative">
                      <select
                        {...register('role')}
                        className="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors appearance-none bg-white text-gray-500 text-base"
                      >
                        <option value="">Select role</option>
                        <option value="admin">Admin</option>
                        <option value="manager">Manager</option>
                        <option value="employee">Employee</option>
                        <option value="finance">Finance Manager</option>
                      </select>
                      {errors.role && (
                        <p className="text-sm text-red-500 mt-1">{errors.role.message}</p>
                      )}
                      <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
                    </div>
                  </div> */}
                </div>
              </div>

              {/* {FormData.role && (
              <div className="mt-12">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                  <div>
                    <h3 className="text-lg font-medium text-gray-800 mb-4">Role Permissions</h3>
                    <p className="text-sm text-gray-600 mb-6">
                      Default permissions for selected role will be applied. You can customize
                      permissions below.
                    </p>

                    <div className="space-y-4">
                      <label className="flex items-center space-x-3">
                        <input
                          name="permissions.viewExpenses"
                          type="checkbox"
                          className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <span className="text-base text-gray-700">View Expenses</span>
                      </label>

                      <label className="flex items-center space-x-3">
                        <input
                          name="permissions.submitExpenses"
                          type="checkbox"
                          className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <span className="text-base text-gray-700">Submit Expenses</span>
                      </label>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-800 mb-4">
                      Customize Permissions (Optional)
                    </h3>

                    <div className="space-y-4">
                      <label className="flex items-center space-x-3">
                        <input
                          name="customPermissions.approveExpenses"
                          type="checkbox"
                          className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <span className="text-base text-gray-700">Approve Expenses</span>
                      </label>

                      <label className="flex items-center space-x-3">
                        <input
                          name="customPermissions.viewAllExpenses"
                          type="checkbox"
                          className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <span className="text-base text-gray-700">View All Expenses</span>
                      </label>

                      <label className="flex items-center space-x-3">
                        <input
                          name="customPermissions.editAllExpenses"
                          type="checkbox"
                          className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <span className="text-base text-gray-700">Edit All Expenses</span>
                      </label>

                      <label className="flex items-center space-x-3">
                        <input
                          name="customPermissions.manageUsers"
                          type="checkbox"
                          className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                        />
                        <span className="text-base text-gray-700">Manage Users</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )} */}

              <div className="flex justify-end mt-12">
                <button
                  type="submit"
                  disabled={isPending}
                  className="px-10 py-4 bg-teal-500 text-white font-medium rounded-xl hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-colors text-base"
                >
                  {isPending ? 'Saving...' : 'Add Employee'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
};

export default AddEmployee;
