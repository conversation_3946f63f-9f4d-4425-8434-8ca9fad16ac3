import { useNavigate } from '@tanstack/react-router';
import { Button } from '../../../components/Button';

const ReportDetailsHeader = () => {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
      <div>
        <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Expense Reports</h1>
        <div className="flex items-center">
          <span
            className="cursor-pointer hover:text-teal-600 transition-colors text-sm text-gray-500 font-semibold"
            onClick={() => navigate({ to: '/' })}
          >
            Dashboard
          </span>
          <span className="mx-2 text-sm text-gray-500">/</span>
          <span
            className="cursor-pointer hover:text-teal-600 transition-colors text-sm text-gray-500 font-semibold"
            onClick={() => navigate({ to: '/expense-reports' })}
          >
            Expense Reports
          </span>
          <span className="mx-2 text-sm text-gray-500">/</span>
          <span className="text-sm text-teal-600 font-semibold">Report Details</span>
        </div>
      </div>

      <Button
        className="brand-gradient px-8 py-3 rounded-sm text-sm text-white"
        onClick={() => navigate({ to: '/expense-reports/employee-report' })}
      >
        Export
      </Button>
    </div>
  );
};

export default ReportDetailsHeader;
