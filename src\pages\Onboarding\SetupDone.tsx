import React from 'react';
import { useNavigate } from '@tanstack/react-router';

const SetupDone: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      <div className="absolute inset-0 z-0 pointer-events-none">
        <img
          src="/expenso-bg-img.svg"
          alt="Background Pattern Bottom Left"
          className="absolute bottom-0 left-0 w-40 sm:w-56 md:w-64 lg:w-72 xl:w-80 h-auto"
        />
        <img
          src="/expenso-bg-img.svg"
          alt="Background Pattern Top Right"
          className="absolute top-0 right-0 w-40 sm:w-56 md:w-64 lg:w-72 xl:w-80 h-auto rotate-180"
        />
      </div>

      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 text-center">
        <div className="flex flex-col items-center mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-14 h-14 sm:w-16 sm:h-16 md:w-20 md:h-20">
              <img
                src="/expenso-logo.png"
                alt="Expenso Logo"
                className="w-full h-full object-contain"
              />
            </div>

            <div className="w-52 sm:w-64 md:w-72">
              <img
                src="/expenso-text.svg"
                alt="Expenso Text"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>

          <p className="text-gray-400 text-sm mt-2 tracking-[1.2em] uppercase text-center">
            Admin Dashboard
          </p>
        </div>

        <div className="mb-12">
          <img
            src="/setup-done.gif"
            alt="Expenso Gear Animation"
            className="w-32 sm:w-40 md:w-48 lg:w-56 h-auto bg-white rounded-full"
          />
        </div>

        <div className="mb-12 max-w-2xl">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#00A6A7] mb-4">
            Awesome, you’re all set up !
          </h2>
          <p className="text-gray-600 text-base sm:text-lg leading-relaxed">
            Expenso helps you manage your company's expenses efficiently. Get started by setting up
            your company information.
          </p>
        </div>

        <button
          className="bg-gradient-to-t from-[#2ADADA] to-[#04A1A2] hover:bg-teal-600 text-white font-semibold py-3 px-6 sm:py-4 sm:px-8 rounded-full text-base sm:text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95"
          onClick={() => navigate({ to: '/' })}
        >
          Go to Dashboard
        </button>
      </div>
    </div>
  );
};

export default SetupDone;
