import { useNavigate } from '@tanstack/react-router';
import { toast } from 'react-toastify';
import type { ApprovalData } from '../../../mockData/mockData';

interface ApprovalTableProps {
  data: ApprovalData[];
}

const ApprovalTable = ({ data }: ApprovalTableProps) => {
  const navigate = useNavigate();

  const navigateToEmployeeDetails = (employeeId: string) => {
    navigate({ to: `/expense-reports/${employeeId}` });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="overflow-x-auto">
        <table className="w-full min-w-[720px] mt-4">
          <thead>
            <tr className="border-b border-gray-200 text-left text-sm text-gray-800">
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Employee Name</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Department</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Designation</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Expense Category</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Submission Date</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Total Amount</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Actions</th>
            </tr>
          </thead>
          <tbody>
            {data.length > 0 ? (
              data.map((approval) => (
                <tr
                  key={approval.id}
                  className="border-b border-gray-100 hover:bg-gray-50 text-nowrap"
                >
                  <td className="py-4 px-2 sm:px-4">
                    <div
                      className="text-sm font-medium text-nowrap text-gray-600 cursor-pointer"
                      onClick={() => navigateToEmployeeDetails(approval.id)}
                    >
                      {approval.employeeName}
                    </div>
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-sm text-gray-600">{approval.department}</td>
                  <td className="py-4 px-2 sm:px-4 text-sm text-gray-600">
                    {approval.designation}
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <img src="/categoryIcon.svg" alt="Category Icon" />
                      <span>{approval.expenseCategory}</span>
                    </div>
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-xs font-semibold text-gray-600">
                    {approval.submissionDate}
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-sm font-medium text-gray-600">
                    ${approval.totalAmount.toFixed(2)}
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <button
                      onClick={() => toast.info('Approval details view coming soon!')}
                      className="text-xs text-nowrap font-bold text-gray-500 hover:text-gray-800 cursor-pointer"
                    >
                      View
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr className="text-nowrap">
                <td colSpan={7} className="py-8 px-2 sm:px-4 text-center text-gray-500">
                  No approvals found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ApprovalTable;
