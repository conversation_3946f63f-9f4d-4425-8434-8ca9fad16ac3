import { z } from "zod";

export const employeeRegistrationSchema  = z.object({
  email: z.string().email("Invalid email").max(254),
  username: z.string().min(1, "Username is required").max(30),
  first_name: z.string().min(1, "First name is required").max(50),
  last_name: z.string().min(1, "Last name is required").max(50),
  phone: z.string().max(20).optional().or(z.literal('').transform(() => undefined)),
  role: z.string().min(1, "Role is required").max(50),
  department: z.string().min(1, "Department is required").max(50),
  manager: z.string().optional(),
  auth_source: z.enum(["local", "ad"]).optional(),
  is_active: z.boolean().optional(),
  expense_limit: z.string().optional(),
  can_approve_expenses: z.boolean().optional(),


      permissions: z
      .object({
        viewExpenses: z.boolean().optional(),
        submitExpenses: z.boolean().optional(),
        editOwnExpenses: z.boolean().optional(),
        approveExpenses: z.boolean().optional(),
        viewAllExpenses: z.boolean().optional(),
        editAllExpenses: z.boolean().optional(),
        manageUsers: z.boolean().optional(),
        generateReports: z.boolean().optional(),
      })
      .optional(),
})

export type EmployeeRegistrationInput  = z.infer<typeof employeeRegistrationSchema >;
