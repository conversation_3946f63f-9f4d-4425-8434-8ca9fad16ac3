import { Outlet } from '@tanstack/react-router';
import Sidebar from '../Sidebar/Sidebar';
import Navbar from '../Navbar/Navbar';

interface LayoutProps {
  children?: React.ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      <main className="flex-1 overflow-auto lg:ml-0 flex flex-col">
        <Navbar />
        <div className="flex-1 container mx-auto">{children || <Outlet />}</div>
      </main>
    </div>
  );
};

export default Layout;
