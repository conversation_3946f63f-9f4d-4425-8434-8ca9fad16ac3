import {
  createRootRoute,
  createRoute,
  createRouter,
  Outlet,
  useNavigate,
} from '@tanstack/react-router';
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools';
import Layout from '../components/layout/MainLayout';
import ProtectedRoute from '../api/ProtectedRoute';
import AuditTrail from '../pages/AuditTrail/AuditTrail';
import Categories from '../pages/Categories/Categories';
import ExpenseReports from '../pages/ExpenseReports/ExpenseReports';
import Home from '../pages/Home/Home';
import Integrations from '../pages/Integrations/Integrations';
import AddEmployeesOptions from '../pages/Onboarding/AddEmployeesOptions';
import Welcome from '../pages/Onboarding/Welcome';
import AddEmployee from '../pages/Onboarding/AddEmployeeForm';
import ADIntegrationSettings from '../pages/Onboarding/ADIntegrationSettings';
import ImportEmployees from '../pages/Onboarding/ImportEmployee';
import LogoUpload from '../pages/Onboarding/LogoUpload';
import OrganizationProfile from '../pages/Onboarding/OrganizationProfile';
import OrganizationReview from '../pages/Onboarding/OrganizationReview';
import SetupDone from '../pages/Onboarding/SetupDone';
import PolicyManagement from '../pages/PolicyManagement/PolicyManagement';
import AddPolicy from '../pages/PolicyManagement/components/AddPolicy';
import Settings from '../pages/Settings/Settings';
import UserManagement from '../pages/UserManagement/UserManagement';
import Login from '../pages/Login/Login';
import AuthPageLayout from '../components/layout/AuthLayout';
import NotFound from '../pages/NotFound/NotFound';
import ReportDetails from '../pages/ReportDetails/ReportDetails';
import BatchExport from '../pages/BatchExport/BatchExport';
import EmployeeExpenseReport from '../pages/EmployeeExpenseReport/EmployeeExpenseReport';
import Signup from '../pages/signup/SignUp';
const rootRoute = createRootRoute({
  component: () => (
    <>
      <Outlet />
      <TanStackRouterDevtools />
    </>
  ),
});
const authRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/auth',
  component: AuthPageLayout,
});
const loginRoute = createRoute({
  getParentRoute: () => authRoute,
  path: '/login',
  component: Login,
});

const signupRoute = createRoute({
  getParentRoute: () => authRoute,
  path: '/signup',
  component: Signup,
});
const indexRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <Home />
      </Layout>
    </ProtectedRoute>
  ),
});

const usersRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/users',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <UserManagement />
      </Layout>
    </ProtectedRoute>
  ),
});

const expenseReportsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/expense-reports',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <ExpenseReports />
      </Layout>
    </ProtectedRoute>
  ),
});

const reportDetailsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/expense-reports/$employeeId',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <ReportDetails />
      </Layout>
    </ProtectedRoute>
  ),
});

const batchExportRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/expense-reports/batch-export',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <BatchExport />
      </Layout>
    </ProtectedRoute>
  ),
});

const employeeReportRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/expense-reports/employee-report',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <EmployeeExpenseReport />
      </Layout>
    </ProtectedRoute>
  ),
});

const employeeExpenseReportRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/employee-expense-report',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <ExpenseReports />
      </Layout>
    </ProtectedRoute>
  ),
});

const policiesRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/policies',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <PolicyManagement />
      </Layout>
    </ProtectedRoute>
  ),
});

const addPolicyRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/policies/add',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <AddPolicy />
      </Layout>
    </ProtectedRoute>
  ),
});

const categoriesRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/categories',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <Categories />
      </Layout>
    </ProtectedRoute>
  ),
});

const integrationsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/integrations',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <Integrations />
      </Layout>
    </ProtectedRoute>
  ),
});

const auditTrailRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/audit-trail',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <AuditTrail />
      </Layout>
    </ProtectedRoute>
  ),
});

const settingsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/settings',
  component: () => (
    <ProtectedRoute>
      <Layout>
        <Settings />
      </Layout>
    </ProtectedRoute>
  ),
});
const onboardingWelcomeRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/onboarding/welcome',
  component: Welcome,
});

const onboardingAddEmployeeFormRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/onboarding/add-employee/form',
  component: AddEmployee,
});

const onboardingAddEmployeesOptionsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/onboarding/add-employees-options',
  component: AddEmployeesOptions,
});

const onboardingAdIntegrationRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/onboarding/ad-integration-settings',
  component: ADIntegrationSettings,
});

const onboardingImportEmployeesRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/onboarding/import-employees',
  component: ImportEmployees,
});

const onboardingLogoUploadRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/onboarding/logo-upload',
  component: LogoUpload,
});

const onboardingOrganizationProfileRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/onboarding/organization-profile',
  component: OrganizationProfile,
});

const onboardingOrganizationReviewRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/onboarding/organization-review',
  component: OrganizationReview,
});

const onboardingSetupCompleteRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/onboarding/setup-complete',
  component: SetupDone,
});
const redirectRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/dashboard',
  component: () => {
    const navigate = useNavigate();
    navigate({ to: '/' });
    return null;
  },
});
const notFoundRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '*',
  component: NotFound,
});
const routeTree = rootRoute.addChildren([
  authRoute.addChildren([loginRoute, signupRoute]),
  indexRoute,
  usersRoute,
  expenseReportsRoute,
  reportDetailsRoute,
  batchExportRoute,
  employeeReportRoute,
  employeeExpenseReportRoute,
  policiesRoute,
  addPolicyRoute,
  categoriesRoute,
  integrationsRoute,
  auditTrailRoute,
  settingsRoute,
  onboardingWelcomeRoute,
  onboardingAddEmployeeFormRoute,
  onboardingAddEmployeesOptionsRoute,
  onboardingAdIntegrationRoute,
  onboardingImportEmployeesRoute,
  onboardingLogoUploadRoute,
  onboardingOrganizationProfileRoute,
  onboardingOrganizationReviewRoute,
  onboardingSetupCompleteRoute,
  redirectRoute,
  notFoundRoute,
]);

export const router = createRouter({ routeTree });
