import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X } from 'lucide-react';
import { toast } from 'react-toastify';
import { useNavigate } from '@tanstack/react-router';

interface UploadedLogo {
  file: File;
  preview: string;
  name: string;
}

const LogoUpload = () => {
  const [uploadedLogo, setUploadedLogo] = useState<UploadedLogo | null>(null);
  const navigate = useNavigate();

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      if (file.size <= 5 * 1024 * 1024) {
        const reader = new FileReader();
        reader.onload = (e: ProgressEvent<FileReader>) => {
          if (e.target && e.target.result) {
            setUploadedLogo({
              file,
              preview: e.target.result as string,
              name: file.name,
            });
          }
        };
        reader.readAsDataURL(file);
      } else {
        toast.error('File size must be less than 5MB');
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
    },
    multiple: false,
    maxFiles: 1,
  });

  const removeLogo = () => {
    setUploadedLogo(null);
  };

  const handleNext = async () => {
    try {
      console.log('Uploading logo...');
      navigate({ to: '/onboarding/organization-review' });
    } catch (error) {
      console.error('Logo upload failed:', error);
    }
  };

  const handlePrevious = () => {
    navigate({ to: '/onboarding/add-employees-options' });

    console.log('Navigate to /onboarding/add-employees-options');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-10 pb-6">
          <div className="flex justify-between items-center mt-8">
            <div className="flex flex-col">
              <div className="flex items-center mb-2">
                <div className="w-14 h-14 mr-3">
                  <img src="/expenso-logo.png" alt="Expenso Logo" className="w-full h-full" />
                </div>

                <div className="w-50 h-10">
                  <img src="/expenso-text.svg" alt="EXPENSO" className="w-full h-full" />
                </div>
              </div>

              <p className="text-xs font-medium text-gray-500 uppercase tracking-[0.9em] ">
                Admin Dashboard
              </p>
            </div>

            <div className="flex flex-col items-start space-y-1">
              <p className="text-sm font-semibold text-gray-900">Setup Progress</p>
              <p className="text-xs text-gray-500">3 of 4 steps completed</p>
              <div className="flex space-x-2">
                {[1, 2, 3, 4].map((step) => (
                  <div
                    key={step}
                    className={`h-2 w-12 rounded-full ${
                      step <= 3 ? 'bg-[#06B217]' : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="flex items-center mt-10">
            <div className="w-5 h-5 bg-white rounded-full" />
          </div>
          <h1 className="text-3xl font-semibold text-gray-600">Upload Your Organization Logo</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-10">
        <div className="bg-white rounded-lg p-10 sm:p-10">
          <div className="mx-auto">
            <div className="mb-8 sm:mb-12">
              <p className="text-base sm:text-lg text-gray-600">
                This logo will be displayed on all expense reports and communications.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              {!uploadedLogo ? (
                <div
                  {...getRootProps()}
                  className={`relative border-2 border-dashed rounded-lg p-12 text-center transition-colors cursor-pointer ${
                    isDragActive
                      ? 'border-teal-500 bg-teal-50'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <input {...getInputProps()} />
                  <div className="flex flex-col items-center space-y-4 sm:space-y-6">
                    <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gray-100 rounded-full flex items-center justify-center">
                      <Upload className="w-8 h-8 sm:w-10 sm:h-10 text-gray-400" />
                    </div>

                    <div className="space-y-4 sm:space-y-3">
                      <h3 className="text-lg sm:text-xl font-semibold text-gray-900">
                        {isDragActive ? 'Drop your logo here' : 'Upload Logo'}
                      </h3>
                      <p className="text-sm sm:text-base text-gray-600">
                        Recommended size: 200×200px. Supported formats: JPG, PNG. Max size: 5MB.
                      </p>
                    </div>

                    <button
                      type="button"
                      className="px-6 sm:px-8 py-2 sm:py-4 bg-slate-700 text-white font-medium rounded-lg hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 transition-colors text-sm sm:text-base"
                    >
                      Browse Files
                    </button>

                    <p className="text-xs sm:text-sm text-gray-500">
                      or drag and drop your file here
                    </p>
                  </div>
                </div>
              ) : (
                <div className="border-2 border-gray-200 rounded-xl p-6 sm:p-8">
                  <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 sm:gap-6">
                    <div className="relative">
                      <img
                        src={uploadedLogo.preview}
                        alt="Uploaded logo"
                        className="w-24 h-24 sm:w-32 sm:h-32 object-cover rounded-lg border border-gray-200"
                      />
                      <button
                        onClick={removeLogo}
                        className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                      >
                        <X className="w-3 h-3 text-white" />
                      </button>
                    </div>

                    <div className="flex-1 text-center sm:text-left">
                      <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
                        Logo Uploaded Successfully
                      </h3>
                      <p className="text-sm sm:text-base text-gray-600 mb-4">{uploadedLogo.name}</p>
                      <div {...getRootProps()} className="inline-block cursor-pointer">
                        <input {...getInputProps()} />
                        <button
                          type="button"
                          className="text-sm sm:text-base text-teal-600 hover:text-teal-700 font-medium"
                        >
                          Replace Logo
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex flex-col sm:flex-row justify-between gap-4 sm:gap-6">
              <button
                onClick={handlePrevious}
                className="w-full sm:w-auto px-8 sm:px-10 py-3 sm:py-4 border-2 border-teal-500 text-teal-600 font-medium rounded-xl hover:bg-teal-50 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-colors text-base order-2 sm:order-1"
              >
                Previous
              </button>

              <button
                onClick={handleNext}
                className="w-full sm:w-auto px-8 sm:px-10 py-3 sm:py-4 bg-teal-500 text-white font-medium rounded-xl hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-colors text-base order-1 sm:order-2"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default LogoUpload;
