import { useNavigate } from '@tanstack/react-router';

const ReportPageHeader = () => {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-8 gap-4">
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate({ to: '..' })}
          className="flex items-center gap-2 text-teal-600 hover:text-teal-700 transition-colors cursor-pointer font-semibold"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
          Back
        </button>
        <h1 className="text-2xl font-bold text-teal-600">Employee Expense Report</h1>
      </div>

      <button
        className="bg-gray-700 hover:bg-gray-800 px-6 py-2 rounded text-sm text-white transition-colors"
        onClick={() => window.print()}
      >
        Download
      </button>
    </div>
  );
};

export default ReportPageHeader;
