import { useNavigate } from '@tanstack/react-router';

const OrganizationReview = () => {
  const navigate = useNavigate();

  const organizationData = {
    companyName: 'Tech Solutions Inc.',
    companyAddress: '123 Innovation Drive, Tech City, CA 90210',
    city: 'Tech City',
    state: 'California',
    postalCode: '90210',
    country: 'United States',
    contactEmail: '<EMAIL>',
    contactPhone: '(*************',
    industry: 'Technology',
    fiscalYearStartDate: 'January 1st',
    companyLogo: '/api/placeholder/240/120',
  };

  const handleComplete = async () => {
    try {
      console.log('Setup completed');
      navigate({ to: '/onboarding/setup-done' });
    } catch (error) {
      console.error('Setup completion failed:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-10 pb-6">
          <div className="flex justify-between items-center mt-8">
            <div className="flex flex-col">
              <div className="flex items-center mb-2">
                <div className="w-14 h-14 mr-3">
                  <img src="/expenso-logo.png" alt="Expenso Logo" className="w-full h-full" />
                </div>

                <div className="w-50 h-10">
                  <img src="/expenso-text.svg" alt="EXPENSO" className="w-full h-full" />
                </div>
              </div>

              <p className="text-xs font-medium text-gray-500 uppercase tracking-[0.9em]">
                Admin Dashboard
              </p>
            </div>

            <div className="flex flex-col items-start space-y-1">
              <p className="text-sm font-semibold text-gray-900">Setup Progress</p>
              <p className="text-xs text-gray-500">4 of 4 steps completed</p>
              <div className="flex space-x-2">
                {[1, 2, 3, 4].map((step) => (
                  <div
                    key={step}
                    className={`h-2 w-12 rounded-full ${
                      step <= 4 ? 'bg-[#06B217]' : 'bg-gray-200'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="flex items-center mt-10">
            <h1 className="text-3xl font-semibold text-gray-600">Review and Complete Setup</h1>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div className="bg-white rounded-lg shadow-sm p-6 sm:p-10">
          <div className="mb-12">
            <h3 className="text-xl font-semibold text-gray-900 mb-8">Organization Details</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
              <div className="space-y-6">
                <DetailItem label="Company Name" value={organizationData.companyName} />
                <DetailItem label="Company Address" value={organizationData.companyAddress} />
                <DetailItem label="Contact Email" value={organizationData.contactEmail} />
                <DetailItem label="Contact Phone Number" value={organizationData.contactPhone} />
              </div>

              <div className="space-y-6">
                <DetailItem label="Industry" value={organizationData.industry} />
                <DetailItem
                  label="Fiscal Year Start Date"
                  value={organizationData.fiscalYearStartDate}
                />
              </div>
            </div>
          </div>

          <div className="mb-12">
            <h3 className="text-xl font-semibold text-gray-900 mb-8">Company Logo</h3>

            <div className="flex items-center justify-center sm:justify-start">
              <div className="w-60 h-32 bg-gradient-to-r from-yellow-200 via-green-200 to-teal-400 rounded-lg flex items-center justify-center shadow-sm">
                <div className="text-center">
                  <div className="w-16 h-16 bg-white rounded-lg flex items-center justify-center mx-auto mb-2 shadow-sm">
                    <div className="w-8 h-8 bg-gradient-to-r from-teal-400 to-teal-600 rounded transform rotate-45"></div>
                  </div>
                  <p className="text-sm font-medium text-gray-700 uppercase tracking-wider">
                    Technology
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-12">
            <h3 className="text-xl font-semibold text-gray-900 mb-8">Employee Summary</h3>

            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex flex-wrap items-center gap-2">
                  <p className="text-base text-gray-700">
                    You have added 5 employees to your organization.
                  </p>
                  <div className="flex gap-4">
                    <span className="text-sm text-gray-500">1 Manager</span>
                    <span className="text-sm text-gray-500">1 Finance</span>
                  </div>
                </div>

                <p className="text-base text-gray-700">
                  You have added 25 employees to your organization.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-teal-500 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-base font-medium text-gray-900">Employee List 2025</p>
                      <p className="text-sm text-gray-500">CSV</p>
                    </div>
                  </div>

                  <button
                    type="button"
                    className="px-6 py-2 bg-gray-700 text-white font-medium rounded-lg hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors text-sm"
                  >
                    Review File
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-between items-center pt-8 border-t border-gray-200 space-y-4 sm:space-y-0">
            <button
              type="button"
              className="w-full sm:w-auto px-8 py-3 border border-teal-500 text-gray-700 font-medium rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-colors text-base"
            >
              Previous
            </button>

            <button
              type="button"
              onClick={handleComplete}
              className="w-full sm:w-auto px-10 py-3 bg-teal-500 text-white font-medium rounded-xl hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition-colors text-base"
            >
              Complete Setup
            </button>
          </div>
        </div>
      </main>
    </div>
  );
};

const DetailItem = ({ label, value }: { label: string; value: string }) => {
  return (
    <div className="space-y-2">
      <p className="text-sm font-medium text-gray-500">{label}</p>
      <p className="text-base font-medium text-gray-900">{value}</p>
    </div>
  );
};

export default OrganizationReview;
