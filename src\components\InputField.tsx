import React from 'react';

interface InputFieldProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label: string;
  error?: string;
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
}

const InputField = React.forwardRef<HTMLInputElement, InputFieldProps>(
  (
    {
      label,
      type = 'text',
      name,
      value,
      onChange,
      onBlur,
      placeholder,
      required,
      size = 'md',
      icon,
      error,
      ...rest
    },
    ref
  ) => {
    const sizeClasses = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-3 text-base',
      lg: 'px-5 py-4 text-lg',
    };

    const iconPadding = {
      sm: 'pl-10',
      md: 'pl-11',
      lg: 'pl-12',
    };

    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
        <div className="relative">
          {icon && (
            <span className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-400 pointer-events-none">
              {icon}
            </span>
          )}
          <input
            ref={ref}
            type={type}
            name={name}
            value={value}
            onChange={onChange}
            onBlur={onBlur}
            placeholder={placeholder}
            required={required}
            className={`w-full border ${
              error ? 'border-red-500' : 'border-gray-300'
            } rounded-md text-sm text-gray-500 font-semibold bg-gray-50 focus:outline-none focus:ring-2 ${
              error ? 'focus:ring-red-500' : 'focus:ring-blue-500'
            } focus:border-transparent transition duration-200 ${sizeClasses[size]} ${
              icon ? iconPadding[size] : ''
            }`}
            {...rest}
          />
        </div>
        {error && <p className="text-red-500 text-sm mt-1 font-medium">{error}</p>}
      </div>
    );
  }
);

InputField.displayName = 'InputField';

export default InputField;
