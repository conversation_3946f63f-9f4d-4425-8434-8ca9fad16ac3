import { useNavigate } from '@tanstack/react-router';
import { toast } from 'react-toastify';
import type { DisputeData } from '../../../mockData/mockData';
import StatusBadge from '../../../components/StatusBadge';

interface DisputesTableProps {
  data: DisputeData[];
}

const DisputesTable = ({ data }: DisputesTableProps) => {
  const navigate = useNavigate();

  const navigateToEmployeeDetails = (employeeId: string) => {
    navigate({ to: `/expense-reports/${employeeId}` });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="overflow-x-auto">
        <table className="w-full min-w-[720px] mt-4">
          <thead>
            <tr className="border-b border-gray-200 text-left text-sm text-gray-800 text-nowrap">
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Name</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Department</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Expense Type</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Dispute Raised On</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Dispute Amount</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Rejected By</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Current Status</th>
              <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Actions</th>
            </tr>
          </thead>
          <tbody>
            {data.length > 0 ? (
              data.map((dispute) => (
                <tr
                  key={dispute.id}
                  className="border-b border-gray-100 hover:bg-gray-50 text-nowrap"
                >
                  <td className="py-4 px-2 sm:px-4">
                    <div
                      className="text-sm font-medium text-nowrap text-gray-600 cursor-pointer"
                      onClick={() => navigateToEmployeeDetails(dispute.id)}
                    >
                      {dispute.name}
                    </div>
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-sm text-gray-600">{dispute.department}</td>
                  <td className="py-4 px-2 sm:px-4 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <img src="/categoryIcon.svg" alt="Category Icon" />
                      <span>{dispute.expenseType}</span>
                    </div>
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-xs font-semibold text-gray-600">
                    {dispute.disputeRaisedOn}
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-sm font-medium text-gray-600">
                    ${dispute.disputeAmount.toFixed(2)}
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <div className="flex items-center bg-gray-100 rounded-full px-3 py-2 w-fit">
                      <span className="text-gray-700 text-xs font-medium">
                        {dispute.rejectedBy}
                      </span>
                      <div className="w-px h-4 bg-teal-400 mx-2" />
                      <img src="/tableErrorIcon.svg" alt="Error Icon" className="h-4" />
                    </div>
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <StatusBadge status={dispute.currentStatus} variant="dispute" />
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <button
                      onClick={() => toast.info('Dispute details view coming soon!')}
                      className="text-xs text-nowrap font-bold text-gray-500 hover:text-gray-800 cursor-pointer"
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr className="text-nowrap">
                <td colSpan={8} className="py-8 px-2 sm:px-4 text-center text-gray-500">
                  No disputes found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DisputesTable;
