import { z } from "zod";

export const companySetupSchema = z.object({
  name: z.string().min(1),
  address: z.string().min(1),
  country: z.string().min(1).max(50),
  city: z.string().min(1).max(100),
  state: z.string().min(1).max(100),
  postal_code: z.string().min(1).max(50),
  email: z.string().email(),
  phone: z.string().min(1).max(50),
  industry: z.enum([
    "SOFTWARE", "FINANCE", "HEALTHCARE", "MANUFACTURING", "RETAIL",
    "EDUCATION", "CONSTRUCTION", "TELECOM", "TRANSPORT", "ENERGY", "OTHER"
  ]).optional(),
  expense_tolerance_percentage: z.string().optional(),
  default_currency: z.string().min(1).max(3).optional(),
});

export type companySetupInput = z.infer<typeof companySetupSchema>;
