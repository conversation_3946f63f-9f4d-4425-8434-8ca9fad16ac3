import React from 'react';
import { Alert<PERSON>ircle, RefreshCw, CheckCircle, Info, AlertTriangle } from 'lucide-react';
import { Button } from './Button';

type StatusType = 'error' | 'success' | 'warning' | 'info';

interface StatusPlaceholderProps {
  type?: StatusType;
  title?: string;
  message?: string;
  onRetry?: () => void;
  showRetryButton?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const StatusPlaceholder: React.FC<StatusPlaceholderProps> = ({
  type = 'error',
  title,
  message,
  onRetry,
  showRetryButton = true,
  className = '',
  children,
}) => {
  const statusConfig = {
    error: {
      icon: AlertCircle,
      defaultTitle: 'Error',
      defaultMessage: 'Something went wrong. Please try again.',
      containerClasses: 'border-red-200 bg-red-50 text-red-800',
      iconClasses: 'text-red-500',
      buttonClasses: 'bg-transparent hover:bg-red-100 text-red-600',
    },
    success: {
      icon: CheckCircle,
      defaultTitle: 'Success',
      defaultMessage: 'Operation completed successfully.',
      containerClasses: 'border-green-200 bg-green-50 text-green-800',
      iconClasses: 'text-green-500',
      buttonClasses: 'bg-transparent hover:bg-green-100 text-green-600',
    },
    warning: {
      icon: AlertTriangle,
      defaultTitle: 'Warning',
      defaultMessage: 'Please review the information below.',
      containerClasses: 'border-yellow-200 bg-yellow-50 text-yellow-800',
      iconClasses: 'text-yellow-500',
      buttonClasses: 'bg-transparent hover:bg-yellow-100 text-yellow-600',
    },
    info: {
      icon: Info,
      defaultTitle: 'Information',
      defaultMessage: 'Here is some important information.',
      containerClasses: 'border-blue-200 bg-blue-50 text-blue-800',
      iconClasses: 'text-blue-500',
      buttonClasses: 'bg-transparent hover:bg-blue-100 text-blue-600',
    },
  };

  const config = statusConfig[type];
  const Icon = config.icon;
  const displayTitle = title || config.defaultTitle;
  const displayMessage = message || config.defaultMessage;

  return (
    <div
      role={type === 'error' ? 'alert' : 'status'}
      className={`w-full border rounded-lg px-4 py-4 shadow-sm flex items-start gap-3 ${config.containerClasses} ${className}`}
    >
      <Icon className={`w-5 h-5 mt-0.5 shrink-0 ${config.iconClasses}`} />

      <div className="flex-1 min-w-0">
        <p className="font-semibold text-sm">{displayTitle}</p>
        <p className="text-sm mt-1 leading-relaxed">{displayMessage}</p>
        {children && <div className="mt-3">{children}</div>}
      </div>

      {showRetryButton && onRetry && type === 'error' && (
        <Button
          onClick={onRetry}
          iconLeft={RefreshCw}
          className={`text-sm px-3 py-1.5 rounded-md font-medium transition-colors ${config.buttonClasses}`}
        >
          Retry
        </Button>
      )}
    </div>
  );
};

export default StatusPlaceholder;
