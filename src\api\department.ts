import api from "../lib/axiosInstance";

export type Department = {
  id: number;
  name: string;
  head: number | null;
  head_name: string;
  employee_count: string;
  created_at: string;
};

type DepartmentResponse = {
  count: number;
  next: string | null;
  previous: string | null;
  results: Department[];
};

export const fetchDepartments = async (): Promise<Department[]> => {
  const response = await api.get<DepartmentResponse>("/api/core/department/");
  return response.data.results; 
};
