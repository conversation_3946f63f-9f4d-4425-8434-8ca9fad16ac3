import { useState } from 'react';
import { useNavigate, useLocation, Link } from '@tanstack/react-router';
import { toast } from 'react-toastify';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import GoProCard from './GoProCard';
import MenuItems from './MenuItems';
import HorizontalWhiteDivider from '../HorizontalWhiteDivider';

const Sidebar: React.FC = () => {
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);
  const navigate = useNavigate();
  const location = useLocation();

  const toggleCollapse = (): void => setIsCollapsed((prev) => !prev);
  const handleLogout = (): void => {
    toast.success('Logged out successfully!');
    // Add actual logout logic here
  };
  const handleNavClick = (path: string): void => {
    navigate({ to: path });
  };

  return (
    <div
      className={`
        relative
        ${isCollapsed ? 'w-18' : 'w-64'} brand-gradient text-white
        transition-all duration-300 ease-in-out
        flex flex-col justify-between min-h-screen
      `}
    >
      <div>
        <div className="flex flex-col items-center my-5 relative">
          <Link to="/">
            <img
              src={!isCollapsed ? '/brandLogo.svg' : '/brandIcon.svg'}
              alt="brandLogo"
              className={`${isCollapsed ? 'h-8' : 'h-11'} mb-1 transition-all duration-300`}
            />
            {!isCollapsed && (
              <p
                className={`text-white text-sm font-light tracking-wider transition-all duration-300`}
              >
                ADMIN DASHBOARD
              </p>
            )}
          </Link>

          <button
            onClick={toggleCollapse}
            className="absolute cursor-pointer -right-3 top-15 -translate-y-1/2 w-6 h-6 brand-gradient rounded-full shadow-md hover:shadow-lg transition-all duration-200 group flex items-center justify-center border-2 border-gray-200"
          >
            {isCollapsed ? (
              <ChevronRight className="h-3 w-3 text-white font-bold" />
            ) : (
              <ChevronLeft className="h-3 w-3 text-white font-bold" />
            )}
          </button>
        </div>

        <HorizontalWhiteDivider />

        <MenuItems
          isCollapsed={isCollapsed}
          currentPath={location.pathname}
          handleNavClick={handleNavClick}
          handleLogout={handleLogout}
        />
      </div>

      <GoProCard isCollapsed={isCollapsed} />
    </div>
  );
};

export default Sidebar;
