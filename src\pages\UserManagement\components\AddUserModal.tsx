import React from 'react';
import Modal from '../../../components/Modal';
import InputField from '../../../components/InputField';
import FormDropdown from '../../../components/FormDropdown';

import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { AxiosError } from 'axios';
import { useEffect } from 'react';
import { employeeRegistrationSchema } from '../../../schema/employeeRegistrationSchema';
import type { EmployeeRegistrationInput } from '../../../schema/employeeRegistrationSchema';

import { useRegisterEmployee } from '../../../hooks/useRegisterEmployee';
import { toast } from 'react-toastify';
import { useDepartments } from '../../../hooks/useDepartments';
import { z } from 'zod';

interface AddUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: (userData: any) => void;
}

const AddUserModal: React.FC<AddUserModalProps> = ({ isOpen, onClose, onSubmit: onSubmitProp }) => {
  const basicPermissions: (keyof NonNullable<EmployeeRegistrationInput['permissions']>)[] = [
    'viewExpenses',
    'submitExpenses',
    'editOwnExpenses',
  ];

  const advancedPermissions: (keyof NonNullable<EmployeeRegistrationInput['permissions']>)[] = [
    'approveExpenses',
    'viewAllExpenses',
    'editAllExpenses',
    'manageUsers',
    'generateReports',
  ];
  const formatLabel = (key: string) =>
    key.replace(/([a-z])([A-Z])/g, '$1 $2').replace(/^./, (s) => s.toUpperCase());

  const { mutate, isPending } = useRegisterEmployee();
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm<z.infer<typeof employeeRegistrationSchema>>({
    resolver: zodResolver(employeeRegistrationSchema),
  });
  useEffect(() => {
    console.log('Form errors:', errors);
  }, [errors]);
  const onSubmit = (data: z.infer<typeof employeeRegistrationSchema>) => {
    console.log('Submitting payload:', data);
    const payload = {
      ...data,
    };

    if (onSubmitProp) {
      onSubmitProp(payload);
    }

    mutate(payload, {
      onSuccess: () => {
        toast.success('Employee added successfully!');
        onClose();
        reset();
      },
      onError: (error: unknown) => {
        const axiosError = error as AxiosError<{ message?: string }>;
        const message = axiosError.response?.data?.message || 'Unknown error occurred';
        toast.error('Something went wrong: ' + message);
      },
    });
  };

  const roles = [
    { label: 'Employee', value: 'Employee' },
    { label: 'Manager', value: 'Manager' },
    { label: 'Admin', value: 'Admin' },
    { label: 'Finance Manager', value: 'Finance Manager' },
  ];
  const { data: departments, refetch } = useDepartments();

  useEffect(() => {
    if (isOpen) {
      refetch();
    }
  }, [isOpen, refetch]);

  const departmentOptions =
    departments?.map((dept) => ({
      label: dept.name,
      value: String(dept.id),
    })) || [];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Add New User"
      className="max-w-4xl"
      disableBackdropClick
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <InputField
            label="First Name"
            {...register('first_name')}
            error={errors.first_name?.message}
            placeholder="Enter first name"
          />
          <InputField
            label="User Name"
            {...register('username')}
            error={errors.username?.message}
            placeholder="Enter user name"
          />

          {/* <Controller
            name="manager"
            control={control}
            render={({ field }) => (
              <FormDropdown
                {...field}
                value={field.value?.toString() || ''}
                onChange={(val) => field.onChange(val)}
                options={managers} // Must be string[] of IDs or labels
                label="Manager"
                dropdownSize="sm"
                error={errors.manager?.message}
              />
            )}
          /> */}

          <InputField
            label="Last Name"
            {...register('last_name')}
            error={errors.last_name?.message}
            placeholder="Enter last name"
          />

          <Controller
            name="department"
            control={control}
            render={({ field }) => (
              <FormDropdown
                label="Department"
                value={field.value ?? ''} // number
                onChange={(val) => field.onChange(val.toString())} //  force string
                options={departmentOptions}
                placeholder="Select department"
                error={errors.department?.message}
              />
            )}
          />

          <InputField
            label="Email Address"
            type="email"
            {...register('email')}
            error={errors.email?.message}
            placeholder="Enter email address"
          />

          <Controller
            name="role"
            control={control}
            render={({ field }) => (
              <FormDropdown
                {...field}
                value={field.value?.toString() || ''}
                onChange={(val) => field.onChange(val)}
                label="Role"
                options={roles}
                placeholder="Select role"
                error={errors.role?.message}
              />
            )}
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 pt-2">
          <div>
            <div className="text-md font-bold text-gray-500 mb-5">Role Permissions</div>
            <p className="text-sm text-gray-900 mb-8">
              Default permissions for selected role will be applied. You can customize permissions
              below.
            </p>
            <div className="space-y-3">
              {basicPermissions.map((perm) => (
                <Controller
                  key={perm}
                  name={`permissions.${perm}`}
                  control={control}
                  defaultValue={false}
                  render={({ field }) => (
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={(e) => field.onChange(e.target.checked)}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{formatLabel(perm)}</span>
                    </label>
                  )}
                />
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-md font-bold text-gray-500 mb-8">
              Customize Permissions (Optional)
            </h3>
            <div className="space-y-3">
              {advancedPermissions.map((perm) => (
                <Controller
                  key={perm}
                  name={`permissions.${perm}`}
                  control={control}
                  defaultValue={false}
                  render={({ field }) => (
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={(e) => field.onChange(e.target.checked)}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{formatLabel(perm)}</span>
                    </label>
                  )}
                />
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-6">
          <button
            type="submit"
            className="brand-gradient text-white px-24 py-3 rounded-md font-medium"
            disabled={isPending}
          >
            {isPending ? 'Saving...' : 'Add Employee'}
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default AddUserModal;
