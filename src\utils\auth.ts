export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('access_token');

  if (!token) {
    return false;
  }
  try {
    return token.length > 0;
  } catch (error) {
    console.error('Token validation error:', error);
    return false;
  }
};

export const getAuthToken = (): string | null => {
  return localStorage.getItem('access_token');
};

export const setAuthToken = (token: string): void => {
  localStorage.setItem('access_token', token);
};

export const removeAuthToken = (): void => {
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
};

export const logout = (): void => {
  removeAuthToken();
  window.location.href = '/auth/login';
};
